<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\Clinic;
use Illuminate\Support\ServiceProvider;
use Laravel\Pennant\Feature;

final class FeatureFlagServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Feature::define('monite-integration', function (?Clinic $clinic = null) {
            // Default to false for all clinics unless explicitly enabled
            // This allows granular control over which clinics have access
            return false;
        });
    }
}
