<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Shipment;
use App\Models\SubOrder;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Shipment>
 */
final class ShipmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'sub_order_id' => SubOrder::factory(),
            'tracking_number' => $this->faker->uuid,
            'carrier' => $this->faker->company,
            'tracking_link' => $this->faker->optional()->url,
            'eta_date_from' => $this->faker->optional()->dateTimeBetween('now', '+2 weeks'),
            'eta_date_to' => $this->faker->optional()->dateTimeBetween('now', '+2 weeks'),
            'date_delivered' => $this->faker->optional()->dateTimeBetween('now', '+2 weeks'),
        ];
    }
}
