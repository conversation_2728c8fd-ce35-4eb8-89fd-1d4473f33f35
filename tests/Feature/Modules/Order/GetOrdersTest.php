<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use Illuminate\Support\Carbon;

beforeEach(function () {
    $account = ClinicAccount::factory()->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($account, 'account')->create();

    $vendor = Vendor::factory()->create();

    // Create regular orders
    $items = OrderItem::factory()->count(2)
        ->recycle($vendor)
        ->state([
            'quantity' => 2,
            'price' => 15000,
        ]);

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has($items, 'items')
        ->createMany([
            ['created_at' => Carbon::parse('2025-01-01')],
            ['created_at' => Carbon::parse('2025-01-02')],
            ['created_at' => Carbon::parse('2025-01-03')],
        ]);

    // Create order with backordered items
    $backorderedItems = OrderItem::factory()->count(1)
        ->recycle($vendor)
        ->state([
            'quantity' => 1,
            'price' => 10000,
            'status' => App\Enums\OrderItemStatus::Backordered,
        ]);

    Order::factory()
        ->for($this->clinic, 'clinic')
        ->has($backorderedItems, 'items')
        ->create(['created_at' => Carbon::parse('2025-01-04')]);
});

test('api contract', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/orders');

    $response->assertOk()
        ->assertJsonStructure([
            'meta' => [
                'currentPage',
                'firstPageUrl',
                'from',
                'lastPage',
                'lastPageUrl',
                'nextPageUrl',
                'path',
                'perPage',
                'prevPageUrl',
                'to',
                'total',
            ],
            'links' => [
                '*' => [
                    'url',
                    'label',
                    'active',
                ],
            ],
            'data' => [
                '*' => [
                    'id',
                    'orderNumber',
                    'totalPrice',
                    'itemsCount',
                    'vendorsCount',
                    'date',
                    'status',
                ],
            ],
        ]);
});

test('authentication', function () {
    $this->getJson('/api/orders')->assertUnauthorized();
});

test('limit access to user clinic', function () {
    $account = ClinicAccount::factory()->create();

    $user = User::factory()->for($account, 'account')->create();
    $user->assignRole(ClinicAccountRole::Owner);

    $clinic = Clinic::factory()->for($account, 'account')->create();

    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($user)
        ->withHeader('Highfive-Clinic', $clinic->id)
        ->getJson('/api/orders');

    $response->assertOk()
        ->assertJsonCount(0, 'data');
});

test('require highfive-clinic header', function () {
    $this->actingAs($this->user)
        ->getJson('/api/orders')
        ->assertBadRequest()
        ->assertJson([
            'message' => 'The highfive-clinic header is required.',
        ]);
});

test('filter by order number', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/orders?filter[search]=HFO000001');

    $response->assertOk()
        ->assertJsonCount(1, 'data')
        ->assertJson([
            'data' => [
                [
                    'orderNumber' => 'HFO000001',
                    'totalPrice' => '600.00',
                    'itemsCount' => 2,
                    'vendorsCount' => 1,
                    'date' => '2025-01-01',
                ],
            ],
        ]);
});

test('filter by date range', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/orders?filter[date_from]=2025-01-01&filter[date_to]=2025-01-02');

    $response->assertOk()
        ->assertJsonCount(2, 'data');
});

test('filter by backordered items', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/orders?filter[backordered]=true');

    $response->assertOk()
        ->assertJsonCount(1, 'data');
});

test('sort by date', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/orders?sort=-date');

    $response->assertOk()
        ->assertJson([
            'data' => [
                [
                    'date' => '2025-01-04',
                ],
                [
                    'date' => '2025-01-03',
                ],
                [
                    'date' => '2025-01-02',
                ],
                [
                    'date' => '2025-01-01',
                ],
            ],
        ]);
});

test('sort by order number', function () {
    /** @var Illuminate\Testing\TestResponse $response */
    $response = $this->actingAs($this->user)
        ->withHeader('Highfive-Clinic', $this->clinic->id)
        ->getJson('/api/orders?sort=-order_number');

    $response->assertOk()
        ->assertJson([
            'data' => [
                [
                    'orderNumber' => 'HFO000004',
                ],
                [
                    'orderNumber' => 'HFO000003',
                ],
                [
                    'orderNumber' => 'HFO000002',
                ],
                [
                    'orderNumber' => 'HFO000001',
                ],
            ],
        ]);
});
