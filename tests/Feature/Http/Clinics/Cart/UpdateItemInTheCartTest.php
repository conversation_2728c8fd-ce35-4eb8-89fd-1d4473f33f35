<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;

beforeEach(function () {
    $this->vendor = Vendor::factory()->enabled()->create();
    $this->productOffer = ProductOffer::factory()->for($this->vendor)->create();

    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()
        ->create([
            'account_id' => $this->account->id,
        ]);
    $this->clinic = Clinic::factory()
        ->create([
            'clinic_account_id' => $this->account->id,
        ]);
    $this->clinic->productOffers()->attach($this->productOffer, ['price' => $this->productOffer->price]);

    IntegrationConnection::create([
        'vendor_id' => $this->vendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);
});

it('updates the quantity of an item in the cart', function () {
    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 2,
        ]);

    $response->assertOk()
        ->assertJsonStructure([
            'subtotal',
            'itemsCount',
            'uniqueItemsCount',
        ]);

    expect($cart->items()->first()->quantity)->toBe(2);
});

it('removes an item from the cart when quantity is 0', function () {
    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $this->productOffer->id,
            'quantity' => 0,
        ]);

    $response->assertJson([
        'subtotal' => '0.00',
        'itemsCount' => 0,
        'uniqueItemsCount' => 0,
    ]);

    expect($cart->items()->count())->toBe(0);
});

it('adds a note to the cart item', function () {
    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => $this->productOffer->price,
        'quantity' => 1,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $this->productOffer->id,
            'notes' => 'Do not open until Monday',
        ]);

    $response->assertOk()
        ->assertJsonStructure([
            'subtotal',
            'itemsCount',
            'uniqueItemsCount',
        ]);

    expect($cart->items()->first()->notes)->toBe('Do not open until Monday');
});

it('swaps the product of the cart item', function () {
    $productB = ProductOffer::factory()->for($this->vendor)->create([
        'price' => 990,
    ]);

    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 1090,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $productB->id,
        ]);

    $response->assertOk()
        ->assertJson([
            'subtotal' => '19.80',
            'itemsCount' => 2,
            'uniqueItemsCount' => 1,
        ]);

    expect($cart->items()->count())->toBe(1);
    expect($cart->items()->first()->price)->toBe(990);
});

it('swaps the product of the cart item using the correct price and quantity', function () {
    $productB = ProductOffer::factory()->for($this->vendor)->create([
        'price' => 990,
    ]);

    $productB->clinics()->attach($this->clinic->id, ['price' => 999]);

    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 1090,
        'quantity' => 2,
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $productB->id,
        ]);

    $response->assertOk()
        ->assertJson([
            'subtotal' => '19.98',
            'itemsCount' => 2,
            'uniqueItemsCount' => 1,
        ]);

    expect($cart->items()->count())->toBe(1);
    expect($cart->items()->first()->price)->toBe(999);
});

it('adjusts quantity to comply with increments when swapping offers', function () {
    // Create a product offer with increment of 2
    $productB = ProductOffer::factory()->for($this->vendor)->create([
        'price' => 990,
        'increments' => 2,
    ]);

    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 1090,
        'quantity' => 5, // This doesn't comply with increment of 2
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $productB->id,
            'quantity' => 5, // Should be adjusted to 4 (nearest valid increment)
        ]);

    $response->assertOk();

    // Verify the quantity was adjusted to comply with increments
    $cart->refresh();
    expect($cart->items()->count())->toBe(1);
    expect($cart->items()->first()->quantity)->toBe(4); // Adjusted from 5 to 4
    expect($cart->items()->first()->product_offer_id)->toBe($productB->id);
});

it('ensures minimum quantity of one increment when adjusting', function () {
    // Create a product offer with increment of 5
    $productB = ProductOffer::factory()->for($this->vendor)->create([
        'price' => 990,
        'increments' => 5,
    ]);

    $cart = $this->clinic->cart()->firstOrCreate();
    $cart->items()->create([
        'product_offer_id' => $this->productOffer->id,
        'price' => 1090,
        'quantity' => 2, // This doesn't comply with increment of 5
    ]);

    $response = $this->actingAs($this->user)
        ->withHeaders(['highfive-clinic' => $this->clinic->id])
        ->patchJson("/api/cart/cart-items/{$cart->items()->first()->id}", [
            'product_offer_id' => $productB->id,
            'quantity' => 2, // Should be adjusted to 5 (minimum increment)
        ]);

    $response->assertOk();

    // Verify the quantity was adjusted to the minimum increment
    $cart->refresh();
    expect($cart->items()->count())->toBe(1);
    expect($cart->items()->first()->quantity)->toBe(5); // Adjusted from 2 to 5 (minimum)
    expect($cart->items()->first()->product_offer_id)->toBe($productB->id);
});
