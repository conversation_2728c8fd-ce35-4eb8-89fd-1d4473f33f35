<?php

declare(strict_types=1);

namespace Tests;

use App\Modules\Monite\Jobs\CreateOrUpdateMoniteEntityJob;
use App\Modules\Monite\Jobs\CreatePayableFromInvoiceJob;
use App\Modules\Monite\Jobs\DeleteMoniteEntityJob;
use App\Modules\Monite\Jobs\SyncAllVendorCounterpartsJob;
use App\Modules\Monite\Jobs\SyncMoniteRolesJob;
use App\Modules\Monite\Jobs\SyncMoniteUsersJob;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\Bus;

abstract class TestCase extends BaseTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        Bus::fake([
            CreateOrUpdateMoniteEntityJob::class,
            CreatePayableFromInvoiceJob::class,
            DeleteMoniteEntityJob::class,
            SyncAllVendorCounterpartsJob::class,
            SyncMoniteRolesJob::class,
            SyncMoniteUsersJob::class,

        ]);
    }
}
