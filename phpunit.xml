<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
>
    <testsuites>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_KEY" value="base64:9hwS/x2VrV9DPwj6T86y5aojH3oD1t+sMdQMl2zJklU="/>
        <env name="APP_NAME" value="HighFive Testing"/>
        <env name="APP_URL" value="http://localhost"/>
        <env name="APP_DEBUG" value="true"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="DB_CONNECTION" value="pgsql"/>
        <env name="DB_HOST" value="database"/>
        <env name="DB_PORT" value="5432"/>
        <env name="DB_DATABASE" value="highfive_test"/>
        <env name="DB_USERNAME" value="highfive"/>
        <env name="DB_PASSWORD" value="secret"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="SCOUT_DRIVER" value="collection"/>
        <env name="FILESYSTEM_DISK" value="local"/>
        <env name="CACHE_STORE" value="array"/>
        <env name="MONITE_CLIENT_ID" value="test_client_id"/>
        <env name="MONITE_CLIENT_SECRET" value="test_client_secret"/>
    </php>
</phpunit>
