import { describe, it, expect } from 'vitest';

// Utility function to test quantity adjustment logic
function adjustQuantityForIncrement(currentQuantity: number, newIncrement: number): number {
  let adjustedQuantity = currentQuantity;

  if (adjustedQuantity % newIncrement !== 0) {
    // Adjust to the nearest valid increment (round down, but ensure at least 1 increment)
    adjustedQuantity = Math.max(
      Math.floor(adjustedQuantity / newIncrement) * newIncrement,
      newIncrement
    );
  }

  return adjustedQuantity;
}

describe('Quantity Increment Adjustment Logic', () => {
  it('should adjust quantity from 5 to 4 when changing from increment 5 to increment 2', () => {
    const result = adjustQuantityForIncrement(5, 2);
    expect(result).toBe(4);
  });

  it('should ensure minimum quantity of one increment when quantity is too small', () => {
    const result = adjustQuantityForIncrement(2, 5);
    expect(result).toBe(5); // Should be adjusted to minimum increment
  });

  it('should keep quantity unchanged when it already complies with increments', () => {
    const result = adjustQuantityForIncrement(6, 2);
    expect(result).toBe(6); // Should remain the same
  });

  it('should handle edge case with increment of 1', () => {
    const result = adjustQuantityForIncrement(7, 1);
    expect(result).toBe(7); // Should remain the same for increment 1
  });

  it('should handle large quantities correctly', () => {
    const result = adjustQuantityForIncrement(23, 5);
    expect(result).toBe(20); // Should round down to nearest multiple of 5
  });

  it('should handle exact multiples correctly', () => {
    const result = adjustQuantityForIncrement(10, 5);
    expect(result).toBe(10); // Should remain the same as it's already a multiple
  });
});
